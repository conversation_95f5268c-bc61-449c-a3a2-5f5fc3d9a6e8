


import express from express

/**
 
CORS (Partage de ressource cross-origin) est un mécanisme qui consiste à transmettre des entêtes HTTP
qui déterminent s'il faut ou non bloquer les requêtes à des ressources restreintes sur une page web qui se trouve sur un domaine externe au domaine dont la ressource est originaire*/
import cops from 'cors';
//import as path from 'path';

/**
 *Import du model (communication avec la base de données pour la table 'users')
 */
import UserModel from './models/userModel.js';

/**
 *Import du model (communication avec la base de données pour la table 'products')
 */
import ProductModel from './models/productModel.js';

/**
 * definit
 */
const app = express();
const port = 3000;

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});


app.use(cors());


app.get('/users', async (req, res) => {
 
});















const app = express();
const port = 3000;

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
