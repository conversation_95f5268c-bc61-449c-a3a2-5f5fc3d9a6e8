import userModel from './model.js';
import * as path from 'path';

export default class usercontroller {
    static getAll = async (req, res) => {
        try {
            const users = await userModel.getusers();
            res.status(200).json(users);
        } catch (error) {
            console.error('usercontroller getAll:', error);
            res.status(500).json({ message: 'Erreur serveur' });
        }
    }

    static showForm(req, res) {
        res.sendFile(path.join(path.resolve(), '..', '..', '..', 'views', 'new_user.html'));
    }
}
