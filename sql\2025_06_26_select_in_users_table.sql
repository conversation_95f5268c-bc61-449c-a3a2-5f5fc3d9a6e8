-- recherche  toutes les informations des utilisateurs
SELECT * FROM users;

-- recherche toutes les information d'un user avec son id
SELECT * FROM users WHERE id = 1;

SELECT user.id, username, email, name, description FROM users
JOIN roles ON users.role_id = roles.id
WHERE users.id = 1;

-- recherche d'un user.id , username d'un user avec son id
SELECT id, username FROM users WHERE id = 1;

-- recherche d'un user.id , username, email, name ET role_name pour tous les utilisateurs 
SELECT users.id, username, email, name, description FROM users
JOIN roles ON users.role_id = roles.id;

-- recherche d'un user.id , username, email, name ET role_name pour tous les utilisateurs avec les roles id 2
SELECT users.id, username, email, role_name
FROM users
JOIN roles ON users.role_id = roles.id
WHERE roles.role_name= 'ADMIN';























