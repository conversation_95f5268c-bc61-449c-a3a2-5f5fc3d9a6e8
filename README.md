# STD_APP
mkdir data sql
touch data/data.json
touch sql/init-db.sql

echo
## installation node
npm init
npm install 
npm install nodemon
npm install express

+Permet de gerer proprement les url et middlewares

npm install cors 

La spécification CORS permet aux applications Web clientes chargées dans un domaine particulier d'interagir avec les ressources d'un autre domaine. Cela est utile, car les applications complexes font souvent référence à des API et à des ressources tierces dans leur code côté client.

npm install mysql

npm install express-session

*Gestion des sessions dans le back nodeJs

...





















....