ALTER TABLE `uploads` ADD `user_id` INT NULL AFTER `mime_type`;

CREATE TABLE `uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(60) NOT NULL,
  `path` varchar(100) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE `uploads` ADD `user_id` INT NULL AFTER `mime_type`;

INSERT INTO `uploads` (`filename`, `tmp_filename`, `path`, `mime_type`, `user_id`) VALUES
('image1.png','012563image1.png', 'public/img', 'image/png',3),
('imageAdmin2.png','646465image2.png', 'public/img', 'image/png',1),
('image3.png','684hgfj8487image3.png', 'public/img', 'image/png',2),
('image4.png','46gd416844image4.png', 'public/img', 'image/png',3),

('image185.png','012563image1.png', 'public/img', 'image/png',3),
('imageAdmin27.png','646gdf465image2.png', 'public/img', 'image/png',1),
('image365.png','684fsd68487image3.png', 'public/img', 'image/png',2),
('image14.png','46416sqs844image4.png', 'public/img', 'image/png',3),

('image151.png','01254563image1.png', 'public/img', 'image/png',3),
('imageAdmin29.png','646465image2.png', 'public/img', 'image/png',1),
('image1233.png','68468487image3.png', 'public/img', 'image/png',2),
('image65744.png','46416844image4.png', 'public/img', 'image/png',3),
('image134.jpeg','6843131351image4.jpeg', 'public/img', 'image/jpeg',3);

-- mise à jour de la table uploads
UPDATE `uploads` SET `user_id` = 1 WHERE `id` = 1;
UPDATE `uploads` SET `user_id` = 2 WHERE `id` = 2;
UPDATE `uploads` SET `user_id` = 3 WHERE `id` = 3;
UPDATE `uploads` SET `user_id` = 4 WHERE `id` = 4;
