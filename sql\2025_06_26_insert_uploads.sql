-- Insertion des données d'exemple pour la table uploads
INSERT INTO `uploads` (`id`, `filename`, `tmp_filename`, `path`, `mime_type`) VALUES
(1, 'image1.jpg', 'tmp_image1.jpg', 'public/img', 'image/jpeg'),
(2, 'image2.jpg', 'tmp_image2.jpg', 'public/img', 'image/jpeg'),
(3, 'image3.jpg', 'tmp_image3.jpg', 'public/img', 'image/jpeg'),
(4, 'image4.png', 'tmp_image4.png', 'public/img', 'image/png'),
(5, 'image5.jpg', 'tmp_image5.jpg', 'public/img', 'image/jpeg');

-- apres modification de la table uploads
INSERT INTO `uploads` (`id`, `filename`, `tmp_filename`, `path`, `mime_type`, `user_id`) VALUES
(1, 'image1.jpg', 'tmp_image1.jpg', 'public/img', 'image/jpeg', 1),
(2, 'image2.jpg', 'tmp_image2.jpg', 'public/img', 'image/jpeg', 2),
(3, 'image3.jpg', 'tmp_image3.jpg', 'public/img', 'image/jpeg', 1),
(4, 'image4.png', 'tmp_image4.png', 'public/img', 'image/png', 3),
(5, 'image5.jpg', 'tmp_image5.jpg', 'public/img', 'image/jpeg', 2);