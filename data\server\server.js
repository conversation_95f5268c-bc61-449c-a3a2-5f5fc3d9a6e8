/**
 * Express est un cadre d'application web pour Node.js minimal et flexible qui fournit un ensemble robuste de fonctionnalités pour les applications web et mobiles.
 */


import express from 'express';

/**
 * CORS (Partage de ressource cross-origin) est un mécanisme qui consiste à transmettre des entêtes HTTP
 * qui déterminent s'il faut ou non bloquer les requêtes à des ressources restreintes sur une page web qui se trouve sur un domaine externe au domaine dont la ressource est originaire
 */
import cors from 'cors';
import path from 'path';

import UserRoutes from './modules/users/routes.js';

/**
 *Import du model (communication avec la base de données pour la table 'users')
 */
import UserModel from './models/userModel.js';

/**
 *Import du model (communication avec la base de données pour la table 'products')
 */
// import ProductModel from './models/productModel.js'; // Fichier non créé encore

/**
 * Définit l'application Express et le port
 */
const app = express();
const port = 3000;

// Configuration des middlewares
app.use(cors());
app.use(express.json());

// Démarrage du serveur
app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});


// Routes
app.get('/users', async (req, res) => {
  try {
    const users = await UserModel.getusers();
    res.json(users);
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});
/**
 * Point d'entree 
 */
app.get('/', (req, res) => {
  res.sendFile(path.join(path.resolve(), '..', '..', 'views', 'index.html'));
});
/**
 * definition de ce qui sera affiché a l'url '/' top - utilisation d'express
 */
//app.get('/api/users', async (_, res) => {
  //try {
   // const allUsers = await UserModel.getusers();
   // console.log("INFO!!!", allUsers);
   // res.status(200).json(allUsers);
  //} catch (error) {
   // console.log(error);
   // res.status(500).json({ error: 'Erreur serveur' });
 // }
//});

/**
 * Definition de ce qui se passe sur l'url /category/top - utilisation d'express
 */
app.get('/page_une', (req, res) => {
    res.sendFile(path.resolve('../../views/page_une.html'));
});

app.use('/users', UserRoutes);


/**
 * Route de debug pour voir les routes disponibles
 */
app.get('/debug/routes', (req, res) => {
    const routes = [
        { method: 'GET', path: '/', description: 'Page d\'accueil' },
        { method: 'GET', path: '/api/users', description: 'Liste des utilisateurs' },
        { method: 'GET', path: '/category/top', description: 'Catégorie top' },
        { method: 'GET', path: '/debug/routes', description: 'Cette page - liste des routes' },
        { method: 'GET', path: '/debug/db', description: 'Test de connexion base de données' }
    ];

    res.json({
        message: 'Routes disponibles dans votre API',
        total: routes.length,
        routes: routes,
        server_status: 'Connecté à MySQL',
        database: 'std_app'
    });
});

/**
 * Route de test pour vérifier la connexion à la base de données
 */
app.get('/debug/db', async (req, res) => {
    try {
        const users = await UserModel.getusers();
        res.json({
            message: 'Connexion à la base de données réussie !',
            database: 'std_app',
            users_count: users.length,
            sample_user: users[0] || null,
            status: 'success'
        });
    } catch (error) {
        res.status(500).json({
            message: 'Erreur de connexion à la base de données',
            error: error.message,
            status: 'error'
        });
    }
});






