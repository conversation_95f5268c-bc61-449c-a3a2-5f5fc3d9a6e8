/**
 * import de la fonction de creation de connexion a mysql
 */
import { createConnection } from 'mysql';

/**
 * ddefinition des infos de connexion 
 */
const dbConfig = {  
   host: 'localhost',
  user: 'root',
  password: '',
  database: 'std_app'
  };

/**
   * déclaration de la clas usermodel pour interragir avec la table 'user'
   */
   class userModel {
    constructor() {
        this.db = createConnection(dbConfig);
        this.db.connect();
    }

    /**
     * methode pour recuperer tous les utilisateurs
     */
    getAllUsers(callback) {
        this.db.query('SELECT * FROM users', callback);
    }

    /**
     * @returns resolution de la promesse executant la requete
     */
    static getusers() {
        return new Promise((resolve, reject) => {
            const connection = createConnection(dbConfig);
            connection.connect(function(err) {
                if (err) {
                    console.error('Erreur de connexion à la base de données:', err);
                    reject(err);
                    return;
                }

                const query = 'SELECT * FROM users';
                connection.query(query, function(err, results) {
                    connection.end(); // Fermer la connexion

                    if (err) {
                        console.error('Erreur lors de l\'exécution de la requête:', err);
                        reject(err);
                        return;
                    }

                    console.log('Résultats de la requête:', results);
                    resolve(results);
                });
            });
        });
    }
   }

   /**
    * export de la classe userModel pour pouvoir l'utiliser ailleurs
  */
 export default userModel;