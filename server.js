


import express from 'express';

/**
 * CORS (Partage de ressource cross-origin) est un mécanisme qui consiste à transmettre des entêtes HTTP
 * qui déterminent s'il faut ou non bloquer les requêtes à des ressources restreintes sur une page web qui se trouve sur un domaine externe au domaine dont la ressource est originaire
 */
import cors from 'cors';
//import path from 'path';

/**
 *Import du model (communication avec la base de données pour la table 'users')
 */
import UserModel from './models/userModel.js';

/**
 *Import du model (communication avec la base de données pour la table 'products')
 */
// import ProductModel from './models/productModel.js'; // Fichier non créé encore

/**
 * Définit l'application Express et le port
 */
const app = express();
const port = 3000;

// Configuration des middlewares
app.use(cors());
app.use(express.json());

// Démarrage du serveur
app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});


// Routes
app.get('/users', async (req, res) => {
  try {
    const users = await UserModel.getusers();
    res.json(users);
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});
/**
 * definition de ce qui sera affiché a l'url '/' top - utilisation d'express
 */
app.get('/page_une', (req, res) => {
  res.sendFile(path.join(path.resolve(), 'views', 'page_une.html'));
});
















