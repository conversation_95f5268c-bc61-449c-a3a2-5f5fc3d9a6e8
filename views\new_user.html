<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouveau Utilisateur - STD App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 10px; padding: 8px 15px; background-color: #f8f9fa; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="nav-links">
        <a href="/page_une">Première page</a>
        <a href="/users">Voir les utilisateurs</a>
        <a href="/">Accueil</a>
    </div>

    <h1>Créer un Nouvel Utilisateur</h1>

    <form action="/users" method="post">
        <div class="form-group">
            <label for="username">Nom d'utilisateur :</label>
            <input type="text" id="username" name="username" required>
        </div>

        <div class="form-group">
            <label for="email">Email :</label>
            <input type="email" id="email" name="email" required>
        </div>

        <div class="form-group">
            <label for="password">Mot de passe :</label>
            <input type="password" id="password" name="password" required>
        </div>

        <div class="form-group">
            <label for="role_id">Rôle :</label>
            <select id="role_id" name="role_id" required>
                <option value="">Sélectionner un rôle</option>
                <option value="1">Administrateur</option>
                <option value="2">Éditeur</option>
                <option value="3">Cus</option>
                <option value="4">Visiteur</option>
            </select>
        </div>

        <button type="submit">Créer l'utilisateur</button>
    </form>
</body>
</html>