<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>page une</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
</head>
<body>
    <h1>page une</h1>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-6">
                <h3>Navigation</h3>
                <div class="d-grid gap-2">
                    <a href="/users/create" class="btn btn-primary">Nouvel utilisateur</a>
                    <a href="/users" class="btn btn-success">Voir les utilisateurs</a>
                    <a href="/debug/routes" class="btn btn-info">Routes disponibles</a>
                    <a href="/" class="btn btn-secondary">Retour accueil</a>
                </div>
            </div>
            <div class="col-md-6">
                <h3>Données utilisateurs</h3>
                <div id="userData">
                    <p>Chargement des données...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <script>
        async function getdata() {
            const url = "/api/users";
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log(data);

                // Afficher les données dans la page
                const userDataDiv = document.getElementById('userData');
                if (data && data.length > 0) {
                    userDataDiv.innerHTML = `
                        <p><strong>Nombre d'utilisateurs :</strong> ${data.length}</p>
                        <ul class="list-group">
                            ${data.map(user => `
                                <li class="list-group-item">
                                    <strong>${user.username}</strong> - ${user.email}
                                    <span class="badge bg-primary">${getRoleName(user.role_id)}</span>
                                </li>
                            `).join('')}
                        </ul>
                    `;
                } else {
                    userDataDiv.innerHTML = '<p class="text-warning">Aucun utilisateur trouvé</p>';
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                document.getElementById('userData').innerHTML =
                    '<p class="text-danger">Erreur lors du chargement des données</p>';
            }
        }

        function getRoleName(roleId) {
            const roles = {1: 'Admin', 2: 'Éditeur', 3: 'Client', 4: 'Visiteur'};
            return roles[roleId] || 'Inconnu';
        }

        getdata();



    </script>


</body>
</html>